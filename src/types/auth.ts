import { z } from "zod/v4";
import { UserRole } from "./database.types";
import { Session } from "@supabase/supabase-js";

// Auth validation schemas
const signUpBaseSchema = z.object({
  emailAddress: z
    .email("Please enter a valid email address")
    .min(1, "Email is required"),
  password: z
    .string()
    .min(6, "Password must be at least 6 characters long")
    .max(100, "Password is too long"),
  confirmPassword: z
    .string()
    .min(1, "Please confirm your password"),
  fullName: z
    .string()
    .min(1, "Full name is required")
    .max(100, "Full name is too long"),
  // .regex(
  //   /^[a-zA-Z\s'-]+$/,
  //   "Full name can only contain letters, spaces, hyphens, and apostrophes",
  // ),
  role: z.enum(UserRole),
});

export const signUpSchema = signUpBaseSchema.refine(
  (data) => data.password === data.confirmPassword,
  {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  },
);

export const signInSchema = z.object({
  emailAddress: z
    .email("Please enter a valid email address")
    .min(1, "Email is required"),
  password: z
    .string()
    .min(1, "Password is required"),
});

export const forgotPasswordSchema = z.object({
  emailAddress: z
    .email("Please enter a valid email address")
    .min(1, "Email is required"),
});

export const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(6, "Password must be at least 6 characters long")
    .max(100, "Password is too long"),
  confirmPassword: z
    .string()
    .min(1, "Please confirm your password"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

// TypeScript types
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type SignInFormData = z.infer<typeof signInSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

// Role options for UI components
export const ROLE_OPTIONS = [
  {
    label: "Patient",
    value: UserRole.patient,
  },
  {
    label: "Caregiver",
    value: UserRole.caregiver,
  },
  {
    label: "Healthcare Provider",
    value: UserRole.healthcare_provider,
  },
] as const;

// Validation error types
export interface ValidationErrors {
  [key: string]: string | undefined;
}

// Form state interface
export interface FormState<T> {
  data: T;
  errors: ValidationErrors;
  isValid: boolean;
  isDirty: boolean;
}

// User types based on database schema and AuthContext usage
export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name?: string;
  phone?: string | null;
  date_of_birth?: string | null;
  avatar_url?: string | null;
  role?: keyof typeof UserRole;
  onboarding_completed?: boolean;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface UserInsert {
  id?: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name?: string;
  phone?: string | null;
  date_of_birth?: string | null;
  avatar_url?: string | null;
  role?: keyof typeof UserRole;
  onboarding_completed?: boolean;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface UserUpdate {
  email?: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  phone?: string | null;
  date_of_birth?: string | null;
  avatar_url?: string | null;
  role?: keyof typeof UserRole;
  onboarding_completed?: boolean;
  updated_at?: string | null;
}

// Auth context type interface
export interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (
    email: string,
    password: string,
    userData: Partial<UserInsert>,
  ) => Promise<{
    data: any;
    error: any;
  }>;
  signIn: (email: string, password: string) => Promise<{
    data: any;
    error: any;
  }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{
    data: any;
    error: any;
  }>;
  updateProfile: (updates: UserUpdate) => Promise<{
    data: any;
    error: any;
  }>;
}
