import { View, ActivityIndicator } from 'react-native';
import { Redirect } from 'expo-router';
import { useAuth } from '@/lib/hooks/useAuth';
import { useThemeColors } from '@/lib/store/selectors';

export default function Index() {
  const { user, loading } = useAuth();
  const colors = useThemeColors();

  console.log('user', { user, loading });

  if (loading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.background,
        }}
      >
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!user) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  if (!user.onboarding_completed) {
    return <Redirect href="/(tabs)/onboarding" />;
  }

  return <Redirect href="/(tabs)" />;
}
