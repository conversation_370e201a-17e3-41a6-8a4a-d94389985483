import { StateCreator } from "zustand";
import { AuthError, PostgrestError, Session } from "@supabase/supabase-js";
import { supabase } from "@/lib/supabase";
import { AppErrorHandler, handleSupabaseError } from "@/lib/errorHandler";
import type { User, UserInsert, UserUpdate } from "@/types/auth";

export interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  profileLoading: boolean;
  isAuthenticated: boolean;
  hasCompletedOnboarding: boolean;
  error: string | null;
}

export interface AuthActions {
  // Core auth methods matching AuthContext API
  signUp: (
    email: string,
    password: string,
    userData: Partial<UserInsert>,
  ) => Promise<{
    data: any;
    error: any;
  }>;
  signIn: (email: string, password: string) => Promise<{
    data: any;
    error: any;
  }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{
    data: any;
    error: any;
  }>;
  generateUserProfile: (user: UserInsert) => Promise<{
    data: any;
    error: any;
  }>;
  updateProfile: (updates: UserUpdate) => Promise<{
    data: any;
    error: any;
  }>;

  // Internal state management methods
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;

  // Initialize auth state and listeners
  initialize: () => Promise<void>;

  // Helper methods
  fetchUserProfile: (userId: string) => Promise<void>;
  createProfileData: (user: UserInsert) => UserInsert;
}

export type AuthSlice = AuthState & AuthActions;

export const createAuthSlice: StateCreator<AuthSlice, [], [], AuthSlice> = (
  set,
  get,
) => ({
  // Initial state
  user: null,
  session: null,
  loading: true,
  profileLoading: false,
  isAuthenticated: false,
  hasCompletedOnboarding: false,
  error: null,

  // Initialize auth state and set up listeners
  initialize: async () => {
    try {
      // Get initial session
      const { data: { session } } = await supabase.auth.getSession();
      set({ session });

      if (session?.user) {
        console.log("session.user", session.user);
        // Attempt to fetch user profile, if it fails, generate a new user profile
        // try {
        //   await get().fetchUserProfile(session.user.id);
        // } catch (error) {
        //   console.error("Error fetching user profile:", error);

        //   // Generate a new user profile
        //   await get().generateUserProfile({
        //     id: session.user.id,
        //     email: session.user.email ?? "",
        //     first_name: session.user.user_metadata?.first_name ?? "",
        //     last_name: session.user.user_metadata?.last_name ?? "",
        //   });
        // }
      } else {
        set({ loading: false });
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (_event, session) => {
        set({ session });

        if (session?.user) {
          try {
            console.log(
              "Fetching user profile in auth change",
              session.user.id,
            );
            await get().fetchUserProfile(session.user.id);
          } catch (error) {
            console.error("Error fetching user profile in auth change:", error);
            // Generate a new user profile if fetch fails
            await get().generateUserProfile({
              id: session.user.id,
              email: session.user.email ?? "",
              first_name: session.user.user_metadata?.first_name ?? "",
              last_name: session.user.user_metadata?.last_name ?? "",
            });
          }
        } else {
          set({
            user: null,
            loading: false,
            isAuthenticated: false,
            hasCompletedOnboarding: false,
          });
        }
      });
    } catch (error) {
      console.error("Auth initialization error:", error);
      set({ loading: false, error: "Failed to initialize authentication" });
    }
  },

  // Fetch user profile from database
  fetchUserProfile: async (userId: string) => {
    console.log("Fetching user profile", userId);

    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        console.error("Error fetching user profile:", error);
        set({ error: "Failed to fetch user profile" });
      } else {
        // Check onboarding status from onboarding_progress table
        const { data: onboardingData } = await supabase
          .from("onboarding_progress")
          .select("is_completed")
          .eq("user_id", userId)
          .single();

        const hasCompletedOnboarding = onboardingData?.is_completed || false;

        set({
          user: data,
          isAuthenticated: true,
          hasCompletedOnboarding,
          loading: false,
        });
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
      set({ loading: false, error: "Failed to fetch user profile" });
    }
  },

  // Sign up new user
  signUp: async (
    email: string,
    password: string,
    userData: Partial<UserInsert>,
  ) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.first_name,
            last_name: userData.last_name,
            full_name: userData.full_name,
          },
        },
      });

      if (error) throw error;

      // Create user profile in profiles table
      if (data.user) {
        await get().generateUserProfile({
          id: data.user.id,
          email: data.user.email ?? "",
          first_name: userData.first_name ?? "",
          last_name: userData.last_name ?? "",
        });

        // Initialize onboarding progress
        await supabase
          .from("onboarding_progress")
          .insert({
            user_id: data.user.id,
            is_completed: false,
            current_step: 1,
          });
      }

      return { data, error: null };
    } catch (error) {
      const appError = handleSupabaseError(
        error as PostgrestError | AuthError | Error,
      );
      AppErrorHandler.reportError(appError, undefined, "sign_up");
      return { data: null, error: appError };
    }
  },

  // Sign in existing user
  signIn: async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error("Sign in error:", error);
      return { data: null, error };
    }
  },

  // Sign out user
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error("Sign out error:", error);
      throw error;
    }
  },

  // Reset password
  resetPassword: async (email: string) => {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: "com.liverhealthapp://reset-password",
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error("Reset password error:", error);
      return { data: null, error };
    }
  },

  generateUserProfile: async (user: UserInsert) => {
    console.log("Generating user profile", user);

    // Prevent concurrent profile generation
    if (get().profileLoading) {
      return {
        data: null,
        error: { message: "Profile generation already in progress" },
      };
    }

    set({ profileLoading: true });

    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        const profileData = get().createProfileData(user);

        // Ensure id is defined for database insert
        if (!profileData.id) {
          throw new Error("User ID is required for profile creation");
        }

        const { data, error } = await supabase
          .from("profiles")
          .insert({
            id: profileData.id,
            email: profileData.email,
            first_name: profileData.first_name,
            last_name: profileData.last_name,
            full_name: profileData.full_name,
            phone: profileData.phone,
            date_of_birth: profileData.date_of_birth,
            avatar_url: profileData.avatar_url,
            created_at: profileData.created_at,
            updated_at: profileData.updated_at,
          })
          .select()
          .single();

        if (error) throw error;

        // Also create onboarding progress entry if user.id exists
        if (user.id) {
          await supabase
            .from("onboarding_progress")
            .insert({
              user_id: user.id,
              is_completed: false,
              current_step: 1,
            });
        }

        // Update the store with the new profile
        set({
          user: data,
          isAuthenticated: true,
          hasCompletedOnboarding: false,
          loading: false,
          profileLoading: false,
        });

        return { data, error: null };
      } catch (error) {
        attempt++;
        console.error(`Profile generation attempt ${attempt} failed:`, error);

        if (attempt >= maxRetries) {
          const appError = handleSupabaseError(
            error as PostgrestError | AuthError | Error,
          );
          AppErrorHandler.reportError(
            appError,
            undefined,
            "generate_user_profile",
          );
          set({
            error: appError.message,
            loading: false,
            profileLoading: false,
          });
          return { data: null, error: appError };
        }

        // Wait before retry (exponential backoff)
        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
      }
    }

    // This should never be reached, but TypeScript requires it
    set({ profileLoading: false });
    return {
      data: null,
      error: { message: "Unexpected error in profile generation" },
    };
  },

  // Update user profile
  updateProfile: async (updates: UserUpdate) => {
    const { user } = get();
    if (!user) throw new Error("No user logged in");

    try {
      const { data, error } = await supabase
        .from("profiles")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id)
        .select()
        .single();

      if (error) throw error;

      set({ user: data });
      return { data, error: null };
    } catch (error) {
      console.error("Update profile error:", error);
      return { data: null, error };
    }
  },

  // State management helpers
  setUser: (user: User | null) => {
    set({ user, isAuthenticated: !!user });
  },

  setSession: (session: Session | null) => {
    set({ session });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },

  // Helper method to create consistent profile data
  createProfileData: (user: UserInsert): UserInsert => {
    const now = new Date().toISOString();

    return {
      id: user.id ?? "",
      email: user.email,
      first_name: user.first_name ?? "",
      last_name: user.last_name ?? "",
      full_name: user.full_name ??
        `${user.first_name ?? ""} ${user.last_name ?? ""}`.trim(),
      phone: user.phone ?? null,
      date_of_birth: user.date_of_birth ?? null,
      avatar_url: user.avatar_url ?? null,
      role: user.role ?? undefined,
      onboarding_completed: false,
      created_at: now,
      updated_at: now,
    };
  },
});
