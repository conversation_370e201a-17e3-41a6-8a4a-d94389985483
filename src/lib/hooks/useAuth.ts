import { useAuthUser, useAuthSession, useAuthLoading, useAuthActions } from '@/lib/store/selectors';
import type { AuthContextType } from '@/types/auth';

/**
 * Custom hook that provides the same interface as the old AuthContext
 * This allows existing components to continue working without changes
 */
export const useAuth = (): AuthContextType => {
  const user = useAuthUser();
  const session = useAuthSession();
  const loading = useAuthLoading();
  const { signUp, signIn, signOut, resetPassword, updateProfile } = useAuthActions();

  return {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updateProfile,
  };
};
